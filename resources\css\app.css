@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for HostPanel */
@layer components {
    /* Enhanced button styles */
    .btn-primary {
        @apply inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150;
    }

    .btn-secondary {
        @apply inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150;
    }

    .btn-danger {
        @apply inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700 focus:bg-red-700 active:bg-red-900 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition ease-in-out duration-150;
    }

    /* Card styles */
    .card {
        @apply overflow-hidden bg-white shadow-lg dark:bg-gray-800 sm:rounded-lg;
    }

    .card-header {
        @apply px-6 py-4 border-b border-gray-200 dark:border-gray-700;
    }

    .card-body {
        @apply p-6;
    }

    /* Status indicators */
    .status-online {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100;
    }

    .status-offline {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100;
    }

    .status-warning {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100;
    }

    /* Progress bars */
    .progress-bar {
        @apply w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700;
    }

    .progress-fill {
        @apply h-2 rounded-full transition-all duration-300 ease-in-out;
    }

    .progress-green {
        @apply bg-green-500;
    }

    .progress-yellow {
        @apply bg-yellow-500;
    }

    .progress-red {
        @apply bg-red-500;
    }

    /* Form enhancements */
    .form-input {
        @apply block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-150 ease-in-out;
    }

    .form-input-error {
        @apply border-red-300 focus:ring-red-500 focus:border-red-500;
    }

    /* Navigation enhancements */
    .nav-link-active {
        @apply border-blue-400 text-gray-900 dark:text-gray-100 focus:border-blue-700;
    }

    .nav-link-inactive {
        @apply border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-700 focus:text-gray-700 dark:focus:text-gray-300 focus:border-gray-300 dark:focus:border-gray-700;
    }

    /* Animations */
    .fade-in {
        animation: fadeIn 0.5s ease-in-out;
    }

    .slide-up {
        animation: slideUp 0.3s ease-out;
    }
}

@layer utilities {
    /* Custom animations */
    @keyframes fadeIn {
        from {
            opacity: 0;
        }
        to {
            opacity: 1;
        }
    }

    @keyframes slideUp {
        from {
            transform: translateY(10px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    /* Gradient backgrounds */
    .gradient-blue-purple {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .gradient-green-blue {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    /* Custom shadows */
    .shadow-card {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .shadow-card-hover {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
    .dark\:gradient-blue-purple {
        background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%);
    }
}
