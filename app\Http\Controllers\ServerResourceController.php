<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Symfony\Component\Process\Process;
use Symfony\Component\Process\Exception\ProcessFailedException;

class ServerResourceController extends Controller
{
    public function index()
{
    $os = PHP_OS_FAMILY; // Bisa 'Windows', 'Linux', 'Darwin', dll

    if ($os === 'Linux' || $os === 'Darwin') {
        // Linux / MacOS

        // CPU load 1 menit
        $cpuLoadRaw = shell_exec('cat /proc/loadavg');
        $cpuLoadParts = explode(' ', $cpuLoadRaw);
        $cpuOneMin = $cpuLoadParts[0] ?? 'N/A';

        // RAM
        $memory = shell_exec("free -m");
        preg_match('/Mem:\s+(\d+)\s+(\d+)\s+(\d+)/', $memory, $matches);
        $totalMem = $matches[1] ?? 0;
        $usedMem = $matches[2] ?? 0;
        $freeMem = $matches[3] ?? 0;

    } elseif ($os === 'Windows') {
        // Windows

        // CPU load bisa dari wmic command
        $cpuRaw = shell_exec('wmic cpu get loadpercentage');
        preg_match('/(\d+)/', $cpuRaw, $cpuMatch);
        $cpuOneMin = $cpuMatch[1] ?? 'N/A';

        // RAM info dari wmic OS
        $memRaw = shell_exec('wmic OS get FreePhysicalMemory,TotalVisibleMemorySize /Value');
        preg_match('/FreePhysicalMemory=(\d+)/', $memRaw, $freeMemMatch);
        preg_match('/TotalVisibleMemorySize=(\d+)/', $memRaw, $totalMemMatch);

        $totalMem = isset($totalMemMatch[1]) ? round($totalMemMatch[1] / 1024) : 0; // dalam MB
        $freeMem = isset($freeMemMatch[1]) ? round($freeMemMatch[1] / 1024) : 0; // dalam MB
        $usedMem = $totalMem - $freeMem;

    } else {
        // OS lain belum didukung
        $cpuOneMin = $totalMem = $usedMem = $freeMem = 'N/A';
    }

    // Disk usage (cross platform)
    $diskTotal = disk_total_space("/");
    $diskFree = disk_free_space("/");
    $diskUsed = $diskTotal - $diskFree;

    return view('server_resources.index', compact(
        'cpuOneMin',
        'totalMem',
        'usedMem',
        'freeMem',
        'diskTotal',
        'diskFree',
        'diskUsed'
    ));
}

}
