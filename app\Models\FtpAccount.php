<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Hash;

class FtpAccount extends Model
{
    use HasFactory;

    protected $fillable = [
        'website_id',
        'username',
        'password',
        'home_directory',
        'is_active',
        'last_login',
    ];

    protected $hidden = [
        'password',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'last_login' => 'datetime',
    ];

    /**
     * Automatically hash password when setting
     */
    public function setPasswordAttribute($value)
    {
        $this->attributes['password'] = Hash::make($value);
    }

    /**
     * Get the website that owns the FTP account.
     */
    public function website()
    {
        return $this->belongsTo(Website::class);
    }

    /**
     * Generate a secure random password
     */
    public static function generatePassword($length = 12)
    {
        $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        $password = '';

        for ($i = 0; $i < $length; $i++) {
            $password .= $characters[random_int(0, strlen($characters) - 1)];
        }

        return $password;
    }

    /**
     * Generate FTP username based on website domain
     */
    public static function generateUsername($domain)
    {
        // Remove dots and make it FTP-friendly
        $username = str_replace(['.', '-'], '_', $domain);
        $username = preg_replace('/[^a-zA-Z0-9_]/', '', $username);

        // Ensure it's not too long (most FTP servers have username limits)
        if (strlen($username) > 20) {
            $username = substr($username, 0, 20);
        }

        return strtolower($username);
    }
}
