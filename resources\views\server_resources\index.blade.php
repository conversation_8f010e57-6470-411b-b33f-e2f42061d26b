<x-app-layout>
    <div class="p-4">

        <div class="container p-4 mx-auto">
            <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
    <!-- CPU Load -->
    <div class="p-6 bg-white shadow rounded-xl">
        <h2 class="mb-2 text-lg font-semibold">CPU Load (1 Menit)</h2>
        <p class="text-2xl font-bold text-blue-600">{{ $cpuOneMin }}</p>
    </div>

    <!-- Memory -->
    <div class="p-6 bg-white shadow rounded-xl">
        <h2 class="mb-2 text-lg font-semibold">Memory (RAM)</h2>
        <p>Total: {{ $totalMem }} MB</p>
        <p>Used: {{ $usedMem }} MB</p>
        <p>Free: {{ $freeMem }} MB</p>
    </div>

    <!-- Disk -->
    <div class="p-6 bg-white shadow rounded-xl">
        <h2 class="mb-2 text-lg font-semibold">Disk Usage</h2>
        <p>Total: {{ number_format($diskTotal / (1024*1024*1024), 2) }} GB</p>
        <p>Used: {{ number_format($diskUsed / (1024*1024*1024), 2) }} GB</p>
        <p>Free: {{ number_format($diskFree / (1024*1024*1024), 2) }} GB</p>
    </div>
</div>
        </div>

    </div>
</x-app-layout>
