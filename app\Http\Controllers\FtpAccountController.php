<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\FtpAccount;
use App\Models\Website;
use Illuminate\Support\Facades\Auth;

class FtpAccountController extends Controller
{
    /**
     * Display a listing of FTP accounts for the authenticated user.
     */
    public function index()
    {
        $user = Auth::user();
        $ftpAccounts = FtpAccount::whereHas('website', function ($query) use ($user) {
            $query->where('user_id', $user->id);
        })->with('website')->get();

        return view('ftp.index', compact('ftpAccounts'));
    }

    /**
     * Show the form for creating a new FTP account.
     */
    public function create()
    {
        $user = Auth::user();
        // Get websites that don't have FTP accounts yet
        $websites = Website::where('user_id', $user->id)
            ->doesntHave('ftpAccount')
            ->get();

        return view('ftp.create', compact('websites'));
    }

    /**
     * Store a newly created FTP account.
     */
    public function store(Request $request)
    {
        $request->validate([
            'website_id' => 'required|exists:websites,id',
        ]);

        $user = Auth::user();
        $website = Website::where('id', $request->website_id)
            ->where('user_id', $user->id)
            ->firstOrFail();

        // Check if FTP account already exists for this website
        if ($website->ftpAccount) {
            return redirect()->route('ftp.index')
                ->with('error', 'FTP account already exists for this website.');
        }

        // Generate username and password
        $username = FtpAccount::generateUsername($website->domain);
        $password = FtpAccount::generatePassword();

        // Create FTP account
        $ftpAccount = FtpAccount::create([
            'website_id' => $website->id,
            'username' => $username,
            'password' => $password, // Will be automatically hashed
            'home_directory' => $website->folder_path,
            'is_active' => true,
        ]);

        return redirect()->route('ftp.index')
            ->with('success', 'FTP account created successfully!')
            ->with('ftp_password', $password); // Show password once
    }

    /**
     * Reset FTP account password.
     */
    public function resetPassword(FtpAccount $ftpAccount)
    {
        $user = Auth::user();

        // Ensure the FTP account belongs to the user
        if ($ftpAccount->website->user_id !== $user->id) {
            abort(403);
        }

        $newPassword = FtpAccount::generatePassword();
        $ftpAccount->update([
            'password' => $newPassword, // Will be automatically hashed
        ]);

        return redirect()->route('ftp.index')
            ->with('success', 'FTP password reset successfully!')
            ->with('ftp_password', $newPassword); // Show new password once
    }

    /**
     * Toggle FTP account status.
     */
    public function toggleStatus(FtpAccount $ftpAccount)
    {
        $user = Auth::user();

        // Ensure the FTP account belongs to the user
        if ($ftpAccount->website->user_id !== $user->id) {
            abort(403);
        }

        $ftpAccount->update([
            'is_active' => !$ftpAccount->is_active,
        ]);

        $status = $ftpAccount->is_active ? 'activated' : 'deactivated';

        return redirect()->route('ftp.index')
            ->with('success', "FTP account {$status} successfully!");
    }

    /**
     * Remove the specified FTP account.
     */
    public function destroy(FtpAccount $ftpAccount)
    {
        $user = Auth::user();

        // Ensure the FTP account belongs to the user
        if ($ftpAccount->website->user_id !== $user->id) {
            abort(403);
        }

        $ftpAccount->delete();

        return redirect()->route('ftp.index')
            ->with('success', 'FTP account deleted successfully!');
    }
}
