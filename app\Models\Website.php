<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Website extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'domain',
        'folder_path',
    ];

    /**
     * Get the user that owns the website.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the FTP account for the website.
     */
    public function ftpAccount()
    {
        return $this->hasOne(FtpAccount::class);
    }
}
