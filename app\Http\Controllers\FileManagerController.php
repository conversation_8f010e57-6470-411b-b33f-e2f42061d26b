<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Website;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Str;

class FileManagerController extends Controller
{
    /**
     * Display file manager for a specific website
     */
    public function index(Website $website, Request $request)
    {
        // Ensure user owns the website
        if ($website->user_id !== Auth::id()) {
            abort(403);
        }

        $path = $request->get('path', '');
        $fullPath = $this->getSecurePath($website, $path);

        if (!File::exists($fullPath)) {
            abort(404, 'Directory not found');
        }

        $items = $this->getDirectoryContents($fullPath);
        $breadcrumbs = $this->getBreadcrumbs($path);

        return view('file-manager.index', compact('website', 'items', 'path', 'breadcrumbs'));
    }

    /**
     * Create a new folder
     */
    public function createFolder(Website $website, Request $request)
    {
        if ($website->user_id !== Auth::id()) {
            abort(403);
        }

        $request->validate([
            'name' => 'required|string|max:255|regex:/^[a-zA-Z0-9._-]+$/',
            'path' => 'nullable|string'
        ]);

        $path = $request->get('path', '');
        $folderName = $request->get('name');
        $fullPath = $this->getSecurePath($website, $path);
        $newFolderPath = $fullPath . '/' . $folderName;

        if (File::exists($newFolderPath)) {
            return back()->with('error', 'Folder already exists');
        }

        File::makeDirectory($newFolderPath, 0755);

        return back()->with('success', "Folder '{$folderName}' created successfully");
    }

    /**
     * Upload files
     */
    public function upload(Website $website, Request $request)
    {
        if ($website->user_id !== Auth::id()) {
            abort(403);
        }

        $request->validate([
            'files.*' => 'required|file|max:10240', // 10MB max per file
            'path' => 'nullable|string'
        ]);

        $path = $request->get('path', '');
        $uploadPath = $this->getSecurePath($website, $path);
        $uploadedFiles = [];

        foreach ($request->file('files') as $file) {
            $filename = $file->getClientOriginalName();
            $file->move($uploadPath, $filename);
            $uploadedFiles[] = $filename;
        }

        $count = count($uploadedFiles);
        return back()->with('success', "{$count} file(s) uploaded successfully");
    }

    /**
     * Download a file
     */
    public function download(Website $website, Request $request)
    {
        if ($website->user_id !== Auth::id()) {
            abort(403);
        }

        $path = $request->get('path', '');
        $filename = $request->get('file');
        $fullPath = $this->getSecurePath($website, $path . '/' . $filename);

        if (!File::exists($fullPath) || File::isDirectory($fullPath)) {
            abort(404);
        }

        return Response::download($fullPath);
    }

    /**
     * Delete files or folders
     */
    public function delete(Website $website, Request $request)
    {
        if ($website->user_id !== Auth::id()) {
            abort(403);
        }

        $request->validate([
            'items' => 'required|array',
            'items.*' => 'string',
            'path' => 'nullable|string'
        ]);

        $path = $request->get('path', '');
        $items = $request->get('items');
        $deletedCount = 0;

        foreach ($items as $item) {
            $fullPath = $this->getSecurePath($website, $path . '/' . $item);

            if (File::exists($fullPath)) {
                if (File::isDirectory($fullPath)) {
                    File::deleteDirectory($fullPath);
                } else {
                    File::delete($fullPath);
                }
                $deletedCount++;
            }
        }

        return back()->with('success', "{$deletedCount} item(s) deleted successfully");
    }

    /**
     * Rename a file or folder
     */
    public function rename(Website $website, Request $request)
    {
        if ($website->user_id !== Auth::id()) {
            abort(403);
        }

        $request->validate([
            'old_name' => 'required|string',
            'new_name' => 'required|string|max:255|regex:/^[a-zA-Z0-9._-]+$/',
            'path' => 'nullable|string'
        ]);

        $path = $request->get('path', '');
        $oldName = $request->get('old_name');
        $newName = $request->get('new_name');

        $oldPath = $this->getSecurePath($website, $path . '/' . $oldName);
        $newPath = $this->getSecurePath($website, $path . '/' . $newName);

        if (!File::exists($oldPath)) {
            return back()->with('error', 'File not found');
        }

        if (File::exists($newPath)) {
            return back()->with('error', 'A file with that name already exists');
        }

        File::move($oldPath, $newPath);

        return back()->with('success', "Renamed '{$oldName}' to '{$newName}'");
    }

    /**
     * Show file editor
     */
    public function edit(Website $website, Request $request)
    {
        if ($website->user_id !== Auth::id()) {
            abort(403);
        }

        $path = $request->get('path', '');
        $filename = $request->get('file');
        $fullPath = $this->getSecurePath($website, $path . '/' . $filename);

        if (!File::exists($fullPath) || File::isDirectory($fullPath)) {
            abort(404);
        }

        // Check if file is editable (text file)
        $editableExtensions = ['txt', 'html', 'htm', 'css', 'js', 'php', 'json', 'xml', 'md', 'yml', 'yaml', 'ini', 'conf'];
        $extension = strtolower(File::extension($fullPath));

        if (!in_array($extension, $editableExtensions) && !$this->isTextFile($fullPath)) {
            return back()->with('error', 'This file type cannot be edited');
        }

        $content = File::get($fullPath);
        $breadcrumbs = $this->getBreadcrumbs($path);

        return view('file-manager.edit', compact('website', 'filename', 'content', 'path', 'breadcrumbs'));
    }

    /**
     * Save edited file
     */
    public function update(Website $website, Request $request)
    {
        if ($website->user_id !== Auth::id()) {
            abort(403);
        }

        $request->validate([
            'content' => 'required|string',
            'path' => 'nullable|string',
            'filename' => 'required|string'
        ]);

        $path = $request->get('path', '');
        $filename = $request->get('filename');
        $content = $request->get('content');
        $fullPath = $this->getSecurePath($website, $path . '/' . $filename);

        if (!File::exists($fullPath) || File::isDirectory($fullPath)) {
            abort(404);
        }

        File::put($fullPath, $content);

        return back()->with('success', "File '{$filename}' saved successfully");
    }

    /**
     * Check if file is a text file
     */
    private function isTextFile($path)
    {
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $path);
        finfo_close($finfo);

        return strpos($mimeType, 'text/') === 0;
    }

    /**
     * Get secure path within website directory
     */
    private function getSecurePath(Website $website, $path = '')
    {
        $basePath = rtrim($website->folder_path, '/');
        $requestedPath = trim($path, '/');

        if (empty($requestedPath)) {
            return $basePath;
        }

        $fullPath = $basePath . '/' . $requestedPath;

        // Security check: ensure path is within website directory
        $realBasePath = realpath($basePath);
        $realFullPath = realpath($fullPath);

        if ($realFullPath === false || !Str::startsWith($realFullPath, $realBasePath)) {
            abort(403, 'Access denied');
        }

        return $fullPath;
    }

    /**
     * Get directory contents
     */
    private function getDirectoryContents($path)
    {
        $items = [];

        if (!File::isDirectory($path)) {
            return $items;
        }

        $files = File::files($path);
        $directories = File::directories($path);

        // Add directories first
        foreach ($directories as $directory) {
            $name = basename($directory);
            $items[] = [
                'name' => $name,
                'type' => 'directory',
                'size' => null,
                'modified' => File::lastModified($directory),
                'permissions' => substr(sprintf('%o', fileperms($directory)), -4),
                'path' => $directory
            ];
        }

        // Add files
        foreach ($files as $file) {
            $name = basename($file);
            $items[] = [
                'name' => $name,
                'type' => 'file',
                'size' => File::size($file),
                'modified' => File::lastModified($file),
                'permissions' => substr(sprintf('%o', fileperms($file)), -4),
                'path' => $file,
                'extension' => File::extension($file)
            ];
        }

        // Sort: directories first, then files, both alphabetically
        usort($items, function($a, $b) {
            if ($a['type'] !== $b['type']) {
                return $a['type'] === 'directory' ? -1 : 1;
            }
            return strcasecmp($a['name'], $b['name']);
        });

        return $items;
    }

    /**
     * Generate breadcrumb navigation
     */
    private function getBreadcrumbs($path)
    {
        $breadcrumbs = [['name' => 'Home', 'path' => '']];

        if (!empty($path)) {
            $parts = explode('/', trim($path, '/'));
            $currentPath = '';

            foreach ($parts as $part) {
                $currentPath .= '/' . $part;
                $breadcrumbs[] = [
                    'name' => $part,
                    'path' => trim($currentPath, '/')
                ];
            }
        }

        return $breadcrumbs;
    }
}
