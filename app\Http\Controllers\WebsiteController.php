<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Website;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;

class WebsiteController extends Controller
{
    public function index()
    {
        $websites = Website::where('user_id', Auth::id())->get();
        return view('websites.index', compact('websites'));
    }

    public function create()
    {
        return view('websites.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'domain' => 'required|unique:websites,domain|regex:/^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/'
        ]);

        $basePath = "/home/<USER>/";
        $domain = $request->domain;
        $fullPath = $basePath . $domain . "/public_html";

        // ✅ Cek dulu apakah folder sudah ada
        if (!File::exists($fullPath)) {
            File::makeDirectory($fullPath, 0755, true);

            $indexPath = $fullPath . "/index.html";
            $content = "<!DOCTYPE html><html><head><title>Under Construction</title></head><body><h1>Website {$domain} berhasil dibuat!</h1><p>Silakan upload konten website Anda.</p></body></html>";

            File::put($indexPath, $content);

        }

        Website::create([
            'user_id' => Auth::id(),
            'domain' => $domain,
            'folder_path' => $fullPath,
        ]);

        return redirect()->route('websites.index')->with('success', 'Website "' . $domain . '" has been created successfully!');
    }

    public function destroy(Website $website)
    {
        // Cek apakah user punya hak akses
        if ($website->user_id !== Auth::id()) {
            abort(403);
        }

        // Hapus folder website
        if (File::exists($website->folder_path)) {
            File::deleteDirectory(dirname($website->folder_path)); // hapus folder domain.com
        }

        // Hapus record dari DB
        $website->delete();

        return redirect()->route('websites.index')->with('success', 'Website "' . $website->domain . '" has been deleted successfully.');
    }


}
