<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex items-center justify-between">
            <h2 class="text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200">
                <?php echo e(__('Server Resources')); ?>

            </h2>
            <div class="text-sm text-gray-500 dark:text-gray-400">
                Real-time system monitoring
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
            <!-- Overview Cards -->
            <div class="grid grid-cols-1 gap-6 mb-8 md:grid-cols-2 lg:grid-cols-3">
                <!-- CPU Load -->
                <div class="overflow-hidden bg-white shadow-lg dark:bg-gray-800 sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                        CPU Load (1 min)
                                    </dt>
                                    <dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                        <?php echo e($cpuOneMin); ?>

                                    </dd>
                                </dl>
                            </div>
                        </div>
                        <div class="mt-4">
                            <?php
                                $cpuPercent = is_numeric($cpuOneMin) ? min(100, $cpuOneMin * 100) : 0;
                                $cpuColor = $cpuPercent > 80 ? 'bg-red-500' : ($cpuPercent > 60 ? 'bg-yellow-500' : 'bg-green-500');
                            ?>
                            <div class="flex items-center">
                                <div class="flex-1 bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                                    <div class="<?php echo e($cpuColor); ?> h-2 rounded-full" style="width: <?php echo e($cpuPercent); ?>%"></div>
                                </div>
                                <span class="ml-2 text-sm text-gray-500 dark:text-gray-400"><?php echo e(number_format($cpuPercent, 1)); ?>%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Memory Usage -->
                <div class="overflow-hidden bg-white shadow-lg dark:bg-gray-800 sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                        Memory (RAM)
                                    </dt>
                                    <dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                        <?php echo e($usedMem); ?> / <?php echo e($totalMem); ?> MB
                                    </dd>
                                </dl>
                            </div>
                        </div>
                        <div class="mt-4">
                            <?php
                                $memPercent = $totalMem > 0 ? ($usedMem / $totalMem) * 100 : 0;
                                $memColor = $memPercent > 80 ? 'bg-red-500' : ($memPercent > 60 ? 'bg-yellow-500' : 'bg-green-500');
                            ?>
                            <div class="flex items-center">
                                <div class="flex-1 bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                                    <div class="<?php echo e($memColor); ?> h-2 rounded-full" style="width: <?php echo e($memPercent); ?>%"></div>
                                </div>
                                <span class="ml-2 text-sm text-gray-500 dark:text-gray-400"><?php echo e(number_format($memPercent, 1)); ?>%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Disk Usage -->
                <div class="overflow-hidden bg-white shadow-lg dark:bg-gray-800 sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                        Disk Storage
                                    </dt>
                                    <dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                        <?php echo e(number_format($diskUsed / (1024*1024*1024), 1)); ?> / <?php echo e(number_format($diskTotal / (1024*1024*1024), 1)); ?> GB
                                    </dd>
                                </dl>
                            </div>
                        </div>
                        <div class="mt-4">
                            <?php
                                $diskPercent = $diskTotal > 0 ? ($diskUsed / $diskTotal) * 100 : 0;
                                $diskColor = $diskPercent > 80 ? 'bg-red-500' : ($diskPercent > 60 ? 'bg-yellow-500' : 'bg-green-500');
                            ?>
                            <div class="flex items-center">
                                <div class="flex-1 bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                                    <div class="<?php echo e($diskColor); ?> h-2 rounded-full" style="width: <?php echo e($diskPercent); ?>%"></div>
                                </div>
                                <span class="ml-2 text-sm text-gray-500 dark:text-gray-400"><?php echo e(number_format($diskPercent, 1)); ?>%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Detailed Information -->
            <div class="grid grid-cols-1 gap-8 lg:grid-cols-2">
                <!-- System Information -->
                <div class="overflow-hidden bg-white shadow-lg dark:bg-gray-800 sm:rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">System Information</h3>
                    </div>
                    <div class="p-6">
                        <dl class="space-y-4">
                            <div class="flex justify-between">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Operating System</dt>
                                <dd class="text-sm text-gray-900 dark:text-gray-100"><?php echo e(PHP_OS_FAMILY); ?></dd>
                            </div>
                            <div class="flex justify-between">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">PHP Version</dt>
                                <dd class="text-sm text-gray-900 dark:text-gray-100"><?php echo e(PHP_VERSION); ?></dd>
                            </div>
                            <div class="flex justify-between">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Server Software</dt>
                                <dd class="text-sm text-gray-900 dark:text-gray-100"><?php echo e($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'); ?></dd>
                            </div>
                            <div class="flex justify-between">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Server Time</dt>
                                <dd class="text-sm text-gray-900 dark:text-gray-100"><?php echo e(now()->format('Y-m-d H:i:s T')); ?></dd>
                            </div>
                            <div class="flex justify-between">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Uptime</dt>
                                <dd class="text-sm text-gray-900 dark:text-gray-100">
                                    <?php if(PHP_OS_FAMILY === 'Linux' || PHP_OS_FAMILY === 'Darwin'): ?>
                                        <?php echo e(trim(shell_exec('uptime -p') ?? 'Unknown')); ?>

                                    <?php else: ?>
                                        Not available on <?php echo e(PHP_OS_FAMILY); ?>

                                    <?php endif; ?>
                                </dd>
                            </div>
                        </dl>
                    </div>
                </div>

                <!-- Resource Details -->
                <div class="overflow-hidden bg-white shadow-lg dark:bg-gray-800 sm:rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">Resource Details</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-6">
                            <!-- Memory Details -->
                            <div>
                                <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Memory Usage</h4>
                                <div class="space-y-2">
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-500 dark:text-gray-400">Total</span>
                                        <span class="text-gray-900 dark:text-gray-100"><?php echo e($totalMem); ?> MB</span>
                                    </div>
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-500 dark:text-gray-400">Used</span>
                                        <span class="text-gray-900 dark:text-gray-100"><?php echo e($usedMem); ?> MB</span>
                                    </div>
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-500 dark:text-gray-400">Free</span>
                                        <span class="text-gray-900 dark:text-gray-100"><?php echo e($freeMem); ?> MB</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Disk Details -->
                            <div>
                                <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Disk Usage</h4>
                                <div class="space-y-2">
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-500 dark:text-gray-400">Total</span>
                                        <span class="text-gray-900 dark:text-gray-100"><?php echo e(number_format($diskTotal / (1024*1024*1024), 2)); ?> GB</span>
                                    </div>
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-500 dark:text-gray-400">Used</span>
                                        <span class="text-gray-900 dark:text-gray-100"><?php echo e(number_format($diskUsed / (1024*1024*1024), 2)); ?> GB</span>
                                    </div>
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-500 dark:text-gray-400">Free</span>
                                        <span class="text-gray-900 dark:text-gray-100"><?php echo e(number_format($diskFree / (1024*1024*1024), 2)); ?> GB</span>
                                    </div>
                                </div>
                            </div>

                            <!-- CPU Details -->
                            <div>
                                <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">CPU Information</h4>
                                <div class="space-y-2">
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-500 dark:text-gray-400">Load Average (1 min)</span>
                                        <span class="text-gray-900 dark:text-gray-100"><?php echo e($cpuOneMin); ?></span>
                                    </div>
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-500 dark:text-gray-400">Cores</span>
                                        <span class="text-gray-900 dark:text-gray-100">
                                            <?php if(function_exists('shell_exec')): ?>
                                                <?php echo e(trim(shell_exec('nproc') ?? 'Unknown')); ?>

                                            <?php else: ?>
                                                Unknown
                                            <?php endif; ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Refresh Notice -->
            <div class="mt-8 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-blue-700 dark:text-blue-300">
                            <strong>Note:</strong> Resource usage data is updated in real-time when you refresh this page.
                            For continuous monitoring, consider setting up automated monitoring tools.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\laragon\www\hosting-panel\resources\views/server_resources/index.blade.php ENDPATH**/ ?>