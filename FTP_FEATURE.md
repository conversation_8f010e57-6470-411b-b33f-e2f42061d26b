# FTP Accounts Feature - Laravel Hosting Panel

## Overview
The FTP Accounts feature allows users to create and manage FTP access for their websites, following web hosting industry best practices.

## Features Implemented

### 1. Database Structure
- **FTP Accounts Table**: Stores FTP credentials and settings
- **Relationships**: One-to-one relationship between websites and FTP accounts
- **Security**: Passwords are automatically hashed using <PERSON><PERSON>'s Hash facade

### 2. Core Functionality
- **Create FTP Account**: Generate FTP credentials for any website
- **View FTP Accounts**: List all FTP accounts with status and details
- **Reset Password**: Generate new secure passwords
- **Toggle Status**: Enable/disable FTP accounts
- **Delete Account**: Remove FTP access completely

### 3. Security Features
- **Auto-generated usernames**: Based on domain names, sanitized for FTP compatibility
- **Strong passwords**: 12-character passwords with mixed case, numbers, and symbols
- **Hashed storage**: Passwords are never stored in plain text
- **User isolation**: Users can only manage their own FTP accounts
- **Directory restriction**: FTP accounts are restricted to their website's directory

### 4. User Experience
- **Intuitive interface**: Clean, modern design consistent with the hosting panel
- **Password display**: New passwords are shown once after creation/reset
- **Toast notifications**: Success/error messages with password display
- **Status indicators**: Visual badges showing account status
- **Quick actions**: Easy access to common FTP management tasks

## Industry Best Practices Followed

### 1. One FTP Account Per Website
- Each website can have only one FTP account
- Prevents confusion and maintains security boundaries
- Follows standard web hosting conventions

### 2. Secure Credential Generation
- **Username format**: `domain_com` (sanitized domain name)
- **Password strength**: 12+ characters with mixed complexity
- **Automatic generation**: No user-chosen weak passwords

### 3. Directory Isolation
- FTP accounts are restricted to their website's `public_html` directory
- Prevents access to other users' files or system directories
- Maintains security boundaries between different websites

### 4. Status Management
- Accounts can be temporarily disabled without deletion
- Useful for maintenance or security purposes
- Preserves configuration while blocking access

## Technical Implementation

### Models
- **FtpAccount**: Main model with relationships and helper methods
- **Website**: Extended with FTP account relationship
- **User**: Indirect relationship through websites

### Controllers
- **FtpAccountController**: Handles all FTP account operations
- **DashboardController**: Updated to show FTP statistics

### Views
- **ftp/index.blade.php**: List all FTP accounts
- **ftp/create.blade.php**: Create new FTP account
- **Enhanced dashboard**: Shows FTP account count and quick actions

### Security
- **Authorization**: Users can only access their own FTP accounts
- **Validation**: Proper input validation and error handling
- **Password hashing**: Automatic password hashing on model save

## Usage Instructions

### Creating an FTP Account
1. Navigate to "FTP Accounts" in the main menu
2. Click "Create FTP Account"
3. Select a website from the dropdown
4. Click "Create FTP Account"
5. **Important**: Save the displayed password immediately

### Managing FTP Accounts
- **Reset Password**: Click the key icon to generate a new password
- **Toggle Status**: Click the lock icon to enable/disable the account
- **Delete Account**: Click the trash icon to permanently remove the account

### FTP Connection Details
- **Server**: Your domain or server IP
- **Port**: 21 (standard FTP)
- **Username**: Generated automatically (e.g., `example_com`)
- **Password**: Provided after account creation
- **Directory**: Automatically set to website's public_html folder

## File Structure
```
app/
├── Models/
│   ├── FtpAccount.php          # FTP account model
│   ├── Website.php             # Updated with FTP relationship
│   └── User.php                # Indirect FTP relationship
├── Http/Controllers/
│   ├── FtpAccountController.php # FTP management controller
│   └── DashboardController.php  # Updated with FTP stats
database/
└── migrations/
    └── create_ftp_accounts_table.php # FTP accounts table
resources/views/
├── ftp/
│   ├── index.blade.php         # FTP accounts list
│   └── create.blade.php        # Create FTP account form
├── components/
│   └── toast.blade.php         # Updated with FTP password display
└── dashboard.blade.php         # Updated with FTP statistics
```

## Future Enhancements
- **SFTP Support**: Add secure FTP protocol options
- **Bandwidth Monitoring**: Track FTP usage statistics
- **File Manager**: Web-based file management interface
- **FTP Logs**: Activity logging and monitoring
- **Quota Management**: Set storage limits per FTP account
- **Multiple Accounts**: Allow multiple FTP accounts per website with different permissions

## Testing Checklist
- [ ] Create FTP account for a website
- [ ] Verify password is displayed once
- [ ] Test password reset functionality
- [ ] Toggle account status (enable/disable)
- [ ] Delete FTP account
- [ ] Verify user isolation (can't access other users' accounts)
- [ ] Check dashboard statistics update
- [ ] Test with no websites available
- [ ] Test with all websites having FTP accounts

## Notes
- This is a basic FTP management system for demonstration purposes
- In production, you would integrate with actual FTP server software (like vsftpd, ProFTPD, etc.)
- The current implementation focuses on the web interface and database management
- Actual FTP server configuration would require additional system-level integration
