<?php

namespace App\Http\Controllers;

use App\Models\Website;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    public function index(){
        $user = Auth::user();

        // Get user's websites
        $websites = Website::where('user_id', $user->id)->latest()->take(5)->get();
        $totalWebsites = Website::where('user_id', $user->id)->count();

        // Get basic server stats
        $diskTotal = disk_total_space("/");
        $diskFree = disk_free_space("/");
        $diskUsed = $diskTotal - $diskFree;
        $diskUsagePercent = round(($diskUsed / $diskTotal) * 100, 1);

        // Get memory info (simplified)
        $os = PHP_OS_FAMILY;
        if ($os === 'Linux' || $os === 'Darwin') {
            $memory = shell_exec("free -m");
            preg_match('/Mem:\s+(\d+)\s+(\d+)\s+(\d+)/', $memory, $matches);
            $totalMem = $matches[1] ?? 0;
            $usedMem = $matches[2] ?? 0;
            $memoryUsagePercent = $totalMem > 0 ? round(($usedMem / $totalMem) * 100, 1) : 0;
        } elseif ($os === 'Windows') {
            $memRaw = shell_exec('wmic OS get FreePhysicalMemory,TotalVisibleMemorySize /Value');
            preg_match('/FreePhysicalMemory=(\d+)/', $memRaw, $freeMemMatch);
            preg_match('/TotalVisibleMemorySize=(\d+)/', $memRaw, $totalMemMatch);
            $totalMem = isset($totalMemMatch[1]) ? round($totalMemMatch[1] / 1024) : 0;
            $freeMem = isset($freeMemMatch[1]) ? round($freeMemMatch[1] / 1024) : 0;
            $usedMem = $totalMem - $freeMem;
            $memoryUsagePercent = $totalMem > 0 ? round(($usedMem / $totalMem) * 100, 1) : 0;
        } else {
            $memoryUsagePercent = 0;
        }

        return view('dashboard', compact(
            'websites',
            'totalWebsites',
            'diskUsagePercent',
            'memoryUsagePercent'
        ));
    }

}
